# Security Setup Guide

## Critical Security Actions Required

### 1. Environment Configuration
```bash
# Copy the environment template
cp .env.example .env

# Edit .env with your actual values - NEVER commit this file
nano .env
```

### 2. Generate Secure Keys
```bash
# Generate SECRET_KEY (for JWT tokens)
python -c "import secrets; print(secrets.token_urlsafe(64))"

# Generate ADMIN_API_KEY  
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### 3. Google Cloud Service Account Setup
```bash
# Create development service account key (DO NOT commit)
# Download from Google Cloud Console and save as:
# ./dev-service-account.json

# Set proper permissions
chmod 600 ./dev-service-account.json
```

### 4. Database Security
- Use strong passwords for PostgreSQL
- Create separate databases for development/test/production
- Never use default credentials

### 5. Verify Security Configuration
```bash
# Check that sensitive files are not tracked
git status --ignored

# Ensure .env files are in .gitignore
grep -n "\.env" .gitignore
```

## Security Violations Fixed

✅ **Removed sensitive files from git tracking:**
- `.env` (contained SECRET_KEY, ADMIN_API_KEY, database passwords)
- `dev-service-account.json` (Google Cloud credentials)
- Service account backup files

✅ **Updated .gitignore to prevent future commits of:**
- Environment files (`.env*`)
- Service account keys (`*-service-account*.json`)
- Credential files (`*credentials*.json`)

✅ **Created secure example templates:**
- `.env.example` with placeholder values
- This security setup guide

## Immediate Actions Required

1. **Regenerate ALL compromised keys:**
   - JWT SECRET_KEY
   - ADMIN_API_KEY
   - Database passwords
   - Service account keys

2. **Review git history for leaked credentials**

3. **Rotate any keys that may have been exposed**

## Ongoing Security Practices

- Never commit `.env` files
- Use strong, unique passwords
- Regularly rotate keys and credentials
- Monitor for unauthorized access
- Use environment-specific configurations