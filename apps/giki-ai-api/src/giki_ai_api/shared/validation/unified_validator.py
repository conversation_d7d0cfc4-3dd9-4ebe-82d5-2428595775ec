"""
Unified Validation System
=========================

Consolidated validation system that merges functionality from:
- Various domain-specific validation logic
- Pydantic model validation
- Business rule validation
- Input sanitization and normalization

This system provides:
- Standardized validation patterns
- Reusable validation rules
- Consistent error messages
- Input sanitization
- Business rule enforcement
- Type-safe validation
"""

import re
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from typing import Any, Dict, List, Optional, Union, Callable, Type
from email_validator import validate_email, EmailNotValidError

from pydantic import BaseModel, ValidationError, Field


@dataclass
class ValidationResult:
    """Result of a validation operation."""
    is_valid: bool
    errors: List[str] = None
    warnings: List[str] = None
    sanitized_value: Any = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.metadata is None:
            self.metadata = {}


class ValidationRule(ABC):
    """Abstract base class for validation rules."""
    
    def __init__(self, error_message: str = None):
        self.error_message = error_message
    
    @abstractmethod
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        """Validate a value and return result."""
        pass


class RequiredRule(ValidationRule):
    """Validates that a value is present and not empty."""
    
    def __init__(self, error_message: str = "This field is required"):
        super().__init__(error_message)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if value is None or (isinstance(value, str) and not value.strip()):
            return ValidationResult(is_valid=False, errors=[self.error_message])
        return ValidationResult(is_valid=True, sanitized_value=value)


class EmailRule(ValidationRule):
    """Validates email addresses."""
    
    def __init__(self, error_message: str = "Please enter a valid email address"):
        super().__init__(error_message)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if not isinstance(value, str):
            return ValidationResult(is_valid=False, errors=["Email must be a string"])
        
        try:
            validated_email = validate_email(value.strip())
            return ValidationResult(
                is_valid=True, 
                sanitized_value=validated_email.email
            )
        except EmailNotValidError:
            return ValidationResult(is_valid=False, errors=[self.error_message])


class PasswordRule(ValidationRule):
    """Validates password strength."""
    
    def __init__(
        self, 
        min_length: int = 8,
        require_uppercase: bool = True,
        require_lowercase: bool = True,
        require_digits: bool = True,
        require_special: bool = True,
        error_message: str = None
    ):
        self.min_length = min_length
        self.require_uppercase = require_uppercase
        self.require_lowercase = require_lowercase
        self.require_digits = require_digits
        self.require_special = require_special
        
        if error_message is None:
            error_message = f"Password must be at least {min_length} characters long"
        super().__init__(error_message)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if not isinstance(value, str):
            return ValidationResult(is_valid=False, errors=["Password must be a string"])
        
        errors = []
        
        if len(value) < self.min_length:
            errors.append(f"Password must be at least {self.min_length} characters long")
        
        if self.require_uppercase and not re.search(r'[A-Z]', value):
            errors.append("Password must contain at least one uppercase letter")
        
        if self.require_lowercase and not re.search(r'[a-z]', value):
            errors.append("Password must contain at least one lowercase letter")
        
        if self.require_digits and not re.search(r'\d', value):
            errors.append("Password must contain at least one digit")
        
        if self.require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', value):
            errors.append("Password must contain at least one special character")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            sanitized_value=value
        )


class NumericRule(ValidationRule):
    """Validates numeric values with optional range checking."""
    
    def __init__(
        self,
        min_value: Union[int, float, Decimal] = None,
        max_value: Union[int, float, Decimal] = None,
        allow_decimal: bool = True,
        error_message: str = None
    ):
        self.min_value = min_value
        self.max_value = max_value
        self.allow_decimal = allow_decimal
        
        if error_message is None:
            error_message = "Please enter a valid number"
        super().__init__(error_message)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        # Try to convert to number
        try:
            if isinstance(value, str):
                if self.allow_decimal:
                    numeric_value = Decimal(value.strip())
                else:
                    numeric_value = int(value.strip())
            elif isinstance(value, (int, float, Decimal)):
                numeric_value = value
            else:
                return ValidationResult(is_valid=False, errors=[self.error_message])
        except (ValueError, InvalidOperation):
            return ValidationResult(is_valid=False, errors=[self.error_message])
        
        errors = []
        
        if self.min_value is not None and numeric_value < self.min_value:
            errors.append(f"Value must be at least {self.min_value}")
        
        if self.max_value is not None and numeric_value > self.max_value:
            errors.append(f"Value must be at most {self.max_value}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            sanitized_value=numeric_value
        )


class DateRule(ValidationRule):
    """Validates date values."""
    
    def __init__(
        self,
        date_format: str = "%Y-%m-%d",
        min_date: date = None,
        max_date: date = None,
        error_message: str = None
    ):
        self.date_format = date_format
        self.min_date = min_date
        self.max_date = max_date
        
        if error_message is None:
            error_message = f"Please enter a valid date in format {date_format}"
        super().__init__(error_message)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        # Try to parse date
        try:
            if isinstance(value, str):
                parsed_date = datetime.strptime(value.strip(), self.date_format).date()
            elif isinstance(value, datetime):
                parsed_date = value.date()
            elif isinstance(value, date):
                parsed_date = value
            else:
                return ValidationResult(is_valid=False, errors=[self.error_message])
        except ValueError:
            return ValidationResult(is_valid=False, errors=[self.error_message])
        
        errors = []
        
        if self.min_date and parsed_date < self.min_date:
            errors.append(f"Date must be on or after {self.min_date}")
        
        if self.max_date and parsed_date > self.max_date:
            errors.append(f"Date must be on or before {self.max_date}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            sanitized_value=parsed_date
        )


class FileRule(ValidationRule):
    """Validates file uploads."""
    
    def __init__(
        self,
        allowed_extensions: List[str] = None,
        max_size_mb: int = None,
        min_size_kb: int = None,
        error_message: str = None
    ):
        self.allowed_extensions = [ext.lower() for ext in (allowed_extensions or [])]
        self.max_size_mb = max_size_mb
        self.min_size_kb = min_size_kb
        
        if error_message is None:
            error_message = "Invalid file"
        super().__init__(error_message)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        # Assume value is a file-like object with name and size attributes
        if not hasattr(value, 'filename') or not hasattr(value, 'size'):
            return ValidationResult(is_valid=False, errors=["Invalid file object"])
        
        errors = []
        
        # Check file extension
        if self.allowed_extensions:
            filename = getattr(value, 'filename', '')
            if filename:
                extension = filename.split('.')[-1].lower() if '.' in filename else ''
                if extension not in self.allowed_extensions:
                    errors.append(f"File type not allowed. Allowed types: {', '.join(self.allowed_extensions)}")
        
        # Check file size
        file_size = getattr(value, 'size', 0)
        
        if self.max_size_mb and file_size > self.max_size_mb * 1024 * 1024:
            errors.append(f"File size must be less than {self.max_size_mb}MB")
        
        if self.min_size_kb and file_size < self.min_size_kb * 1024:
            errors.append(f"File size must be at least {self.min_size_kb}KB")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            sanitized_value=value
        )


class TenantRule(ValidationRule):
    """Validates tenant access and isolation."""
    
    def __init__(self, error_message: str = "Invalid tenant access"):
        super().__init__(error_message)
    
    def validate(self, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        if not isinstance(value, int) or value <= 0:
            return ValidationResult(is_valid=False, errors=["Invalid tenant ID"])
        
        # Additional tenant validation logic could go here
        # For example, checking if tenant exists and is active
        
        return ValidationResult(is_valid=True, sanitized_value=value)


class UnifiedValidator:
    """
    Unified validator that applies multiple validation rules to data.
    """
    
    def __init__(self):
        self.rules: Dict[str, List[ValidationRule]] = {}
        self.sanitizers: Dict[str, List[Callable]] = {}
    
    def add_rule(self, field: str, rule: ValidationRule) -> 'UnifiedValidator':
        """Add a validation rule for a field."""
        if field not in self.rules:
            self.rules[field] = []
        self.rules[field].append(rule)
        return self
    
    def add_sanitizer(self, field: str, sanitizer: Callable) -> 'UnifiedValidator':
        """Add a sanitizer function for a field."""
        if field not in self.sanitizers:
            self.sanitizers[field] = []
        self.sanitizers[field].append(sanitizer)
        return self
    
    def validate_field(self, field: str, value: Any, context: Dict[str, Any] = None) -> ValidationResult:
        """Validate a single field."""
        # Apply sanitizers first
        sanitized_value = value
        if field in self.sanitizers:
            for sanitizer in self.sanitizers[field]:
                sanitized_value = sanitizer(sanitized_value)
        
        # Apply validation rules
        all_errors = []
        all_warnings = []
        
        if field in self.rules:
            for rule in self.rules[field]:
                result = rule.validate(sanitized_value, context)
                if not result.is_valid:
                    all_errors.extend(result.errors)
                all_warnings.extend(result.warnings)
                
                # Update sanitized value if rule provides one
                if result.sanitized_value is not None:
                    sanitized_value = result.sanitized_value
        
        return ValidationResult(
            is_valid=len(all_errors) == 0,
            errors=all_errors,
            warnings=all_warnings,
            sanitized_value=sanitized_value
        )
    
    def validate_data(self, data: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, ValidationResult]:
        """Validate multiple fields."""
        results = {}
        
        # Validate each field that has rules
        for field in self.rules.keys():
            value = data.get(field)
            results[field] = self.validate_field(field, value, context)
        
        return results
    
    def is_valid(self, data: Dict[str, Any], context: Dict[str, Any] = None) -> bool:
        """Check if all data is valid."""
        results = self.validate_data(data, context)
        return all(result.is_valid for result in results.values())
    
    def get_sanitized_data(self, data: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get sanitized version of the data."""
        results = self.validate_data(data, context)
        sanitized = {}
        
        for field, result in results.items():
            if result.sanitized_value is not None:
                sanitized[field] = result.sanitized_value
            else:
                sanitized[field] = data.get(field)
        
        return sanitized


# Common sanitizers
def strip_whitespace(value: Any) -> Any:
    """Remove leading/trailing whitespace from strings."""
    return value.strip() if isinstance(value, str) else value


def lowercase(value: Any) -> Any:
    """Convert string to lowercase."""
    return value.lower() if isinstance(value, str) else value


def normalize_email(value: Any) -> Any:
    """Normalize email address."""
    if isinstance(value, str):
        return value.strip().lower()
    return value


# Pre-configured validators for common use cases
def create_login_validator() -> UnifiedValidator:
    """Create validator for login forms."""
    return (UnifiedValidator()
            .add_sanitizer('email', normalize_email)
            .add_rule('email', RequiredRule())
            .add_rule('email', EmailRule())
            .add_rule('password', RequiredRule()))


def create_registration_validator() -> UnifiedValidator:
    """Create validator for registration forms."""
    return (UnifiedValidator()
            .add_sanitizer('email', normalize_email)
            .add_rule('email', RequiredRule())
            .add_rule('email', EmailRule())
            .add_rule('password', RequiredRule())
            .add_rule('password', PasswordRule()))


def create_transaction_validator() -> UnifiedValidator:
    """Create validator for transaction data."""
    return (UnifiedValidator()
            .add_sanitizer('description', strip_whitespace)
            .add_rule('description', RequiredRule("Transaction description is required"))
            .add_rule('amount', RequiredRule("Transaction amount is required"))
            .add_rule('amount', NumericRule(error_message="Amount must be a valid number"))
            .add_rule('date', RequiredRule("Transaction date is required"))
            .add_rule('date', DateRule())
            .add_rule('tenant_id', RequiredRule("Tenant ID is required"))
            .add_rule('tenant_id', TenantRule()))


def create_file_upload_validator() -> UnifiedValidator:
    """Create validator for file uploads."""
    return (UnifiedValidator()
            .add_rule('file', RequiredRule("File is required"))
            .add_rule('file', FileRule(
                allowed_extensions=['csv', 'xlsx', 'xls'],
                max_size_mb=50,
                min_size_kb=1
            )))


# Global validator instances
login_validator = create_login_validator()
registration_validator = create_registration_validator()
transaction_validator = create_transaction_validator()
file_upload_validator = create_file_upload_validator()


# Validation package init
__all__ = [
    'ValidationResult',
    'ValidationRule',
    'RequiredRule',
    'EmailRule',
    'PasswordRule',
    'NumericRule',
    'DateRule',
    'FileRule',
    'TenantRule',
    'UnifiedValidator',
    'strip_whitespace',
    'lowercase',
    'normalize_email',
    'create_login_validator',
    'create_registration_validator',
    'create_transaction_validator',
    'create_file_upload_validator',
    'login_validator',
    'registration_validator',
    'transaction_validator',
    'file_upload_validator',
]
